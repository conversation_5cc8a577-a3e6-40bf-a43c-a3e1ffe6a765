import { useEffect, useRef } from "react";
import ApexCharts from "apexcharts";

const ExpenseSummaryDashboard = ({
  headerTextStyle = {},
  headingTextStyle = {},
  subHeadingTextStyle = {},
  contentTextStyle = {},
  reportData = null,
}) => {
  const roaRoeRef = useRef(null);
  const expensesPieRef = useRef(null);
  const expensesMonthlyRef = useRef(null);
  const wagesRevenueRef = useRef(null);

  // Helper function to sort data with "Other" last
  const sortDataWithOtherLast = (data) => {
    return data.sort((a, b) => {
      const aIsOther = (a.account_name || '').toLowerCase().includes('other');
      const bIsOther = (b.account_name || '').toLowerCase().includes('other');
      
      if (aIsOther && !bIsOther) return 1;  // a (Other) goes after b
      if (!aIsOther && bIsOther) return -1; // b (Other) goes after a
      return 0; // maintain original order for non-Other items
    });
  };

  // Enhanced data validation function
  const isDataLoaded = () => {
    if (!reportData) {
      console.log("ExpenseSummary - No reportData provided");
      return false;
    }

    console.log("ExpenseSummary - reportData keys:", Object.keys(reportData));

    // Check if at least some required data exists - make it more flexible
    const hasRoeRoaData = reportData.roeRoa && 
                         Array.isArray(reportData.roeRoa) && 
                         reportData.roeRoa.length > 0;
    
    const hasExpensesData = reportData.expensesTopAccounts && 
                           Array.isArray(reportData.expensesTopAccounts) && 
                           reportData.expensesTopAccounts.length > 0;
    
    // For monthly expenses, check for new detailed breakdown data first, then fallback to performance data
    const hasMonthlyExpensesData = reportData.expensesTopAccountsMonthly &&
                                  Array.isArray(reportData.expensesTopAccountsMonthly) &&
                                  reportData.expensesTopAccountsMonthly.length > 0;

    const hasMonthlyData = hasMonthlyExpensesData ||
                          (reportData.monthlyPerformanceBreakDown &&
                           Array.isArray(reportData.monthlyPerformanceBreakDown) &&
                           reportData.monthlyPerformanceBreakDown.length > 0);
    
    // For wages vs revenue, we'll derive from monthly performance data
    const hasPerformanceData = hasMonthlyData;

    console.log("ExpenseSummary - Data validation:", {
      hasRoeRoaData,
      hasExpensesData,
      hasMonthlyExpensesData,
      hasMonthlyData,
      hasPerformanceData,
      roeRoaLength: reportData.roeRoa?.length || 0,
      expensesLength: reportData.expensesTopAccounts?.length || 0,
      monthlyExpensesLength: reportData.expensesTopAccountsMonthly?.length || 0,
      monthlyDataLength: reportData.monthlyPerformanceBreakDown?.length || 0
    });

    // Return true if we have at least some data to work with
    return hasRoeRoaData || hasExpensesData || hasMonthlyData;
  };

  useEffect(() => {
    if (isDataLoaded()) {
      initializeCharts();
    }
  }, [reportData]);

  // Helper function to format month-year
  const formatMonthYear = (year, month) => {
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;
  };

  // Helper function to format currency values with appropriate units
  const formatCurrency = (val, options = {}) => {
    if (val === null || val === undefined || isNaN(val) || val === 0) {
      return options.showZero ? '$0' : '';
    }

    const absVal = Math.abs(val);

    if (absVal >= 1000000) {
      // Trillions
      return '$' + (val / 1000000).toFixed(1) + 't';
    } else if (absVal >= 1000) {
      // Millions
      return '$' + (val / 1000).toFixed(1) + 'm';
    } else {
      // Thousands (since our data is already in thousands)
      return '$' + val.toFixed(1) + 'k';
    }
  };

  // Process ROE/ROA data from API
  const processRoeRoaData = () => {
    if (!reportData?.roeRoa)
      return { roaData: [], roeData: [], categories: [] };

    const roaData = reportData.roeRoa.map((item) => {
      const value = parseFloat(item.roa || 0);
      return isNaN(value) ? 0 : value;
    });

    const roeData = reportData.roeRoa.map((item) => {
      const value = parseFloat(item.roe || 0);
      return isNaN(value) ? 0 : value;
    });

    // Create categories from the data if available
    const categories = reportData.roeRoa.map((item) => {
      if (item.year && item.month) {
        return formatMonthYear(item.year, item.month);
      }
      return item.period || `Period ${reportData.roeRoa.indexOf(item) + 1}`;
    });

    return { roaData, roeData, categories };
  };

  // Process expenses pie chart data from API
  const processExpensesPieData = () => {
    if (!reportData?.expensesTopAccounts) return { data: [], labels: [] };

    // Sort data with "Other" last
    const sortedExpenses = sortDataWithOtherLast([...reportData.expensesTopAccounts]);

    const data = sortedExpenses.map((item) => {
      const value = parseFloat(item.total_expense || 0);
      return isNaN(value) ? 0 : value / 1000; // Convert to thousands
    });

    const labels = sortedExpenses.map((item) => {
      const expense = parseFloat(item.total_expense || 0);
      const percentage = parseFloat(item.percentage_of_total || 0);
      const expenseDisplay = isNaN(expense) ? "0" : (expense / 1000);
      const percentageDisplay = isNaN(percentage) ? "0" : percentage;

      return `${
        item.account_name || "Unknown"
      } ${expenseDisplay}k (${percentageDisplay}%)`;
    });

    return { data, labels };
  };

  // Process monthly expenses data from API - use expensesTopAccountsMonthly for detailed account breakdown
  const processMonthlyExpensesData = () => {
    // Check if we have the new monthly expenses breakdown data
    if (reportData?.expensesTopAccountsMonthly && Array.isArray(reportData.expensesTopAccountsMonthly)) {
      return processDetailedMonthlyExpenses();
    }

    // Fallback to old method if new data is not available
    if (!reportData?.monthlyPerformanceBreakDown) return { series: [], categories: [] };

    // Get monthly data and create categories
    const monthlyData = reportData.monthlyPerformanceBreakDown;
    const categories = monthlyData.map(item =>
      formatMonthYear(item.year, item.month)
    );

    // Since we don't have individual account breakdowns by month,
    // we'll create a single series for total expenses
    const totalExpensesData = monthlyData.map(item => {
      const expense = parseFloat(item.totalExpenses || 0) / 1000; // Convert to thousands
      return isNaN(expense) ? 0 : expense;
    });

    const series = [{
      name: 'Total Expenses',
      data: totalExpensesData
    }];

    return { series, categories };
  };

  // Process detailed monthly expenses breakdown by account
  const processDetailedMonthlyExpenses = () => {
    // Sort data with "Other" last
    const sortedExpensesData = sortDataWithOtherLast([...reportData.expensesTopAccountsMonthly]);

    // Define the month columns in order
    const monthColumns = [
      'apr_24', 'may_24', 'jun_24', 'jul_24', 'aug_24', 'sep_24',
      'oct_24', 'nov_24', 'dec_24', 'jan_25', 'feb_25', 'mar_25'
    ];

    // Create categories (month labels)
    const categories = [
      'Apr 24', 'May 24', 'Jun 24', 'Jul 24', 'Aug 24', 'Sep 24',
      'Oct 24', 'Nov 24', 'Dec 24', 'Jan 25', 'Feb 25', 'Mar 25'
    ];

    // Create series for each account (now sorted with Other last)
    const series = sortedExpensesData.map(account => {
      const data = monthColumns.map(month => {
        // Handle string values from API by parsing them first
        const rawValue = account[month] || '0';
        const value = parseFloat(rawValue) / 1000; // Convert to thousands
        return isNaN(value) ? 0 : value;
      });

      return {
        name: account.account_name || 'Unknown',
        data: data
      };
    });

    return { series, categories };
  };

  // Process wages vs revenue data from monthly performance data
  const processWagesRevenueData = () => {
    if (!reportData?.monthlyPerformanceBreakDown)
      return { income: [], salariesGA: [], salariesSales: [], categories: [] };

    const monthlyData = reportData.monthlyPerformanceBreakDown;

    const income = monthlyData.map((item) => {
      const value = parseFloat(item.totalIncome || 0) / 1000000; // Convert to millions
      return isNaN(value) ? 0 : value;
    });

    // Since we don't have salary breakdown in the data, we'll estimate based on expenses
    // This is a placeholder - you may want to add actual salary data to your API
    const salariesGA = monthlyData.map((item) => {
      const expenses = parseFloat(item.totalExpenses || 0);
      // Estimate G&A salaries as 30% of total expenses
      const value = (expenses * 0.3) / 1000000; // Convert to millions
      return isNaN(value) ? 0 : value;
    });

    const salariesSales = monthlyData.map((item) => {
      const expenses = parseFloat(item.totalExpenses || 0);
      // Estimate Sales salaries as 20% of total expenses
      const value = (expenses * 0.2) / 1000000; // Convert to millions
      return isNaN(value) ? 0 : value;
    });

    const categories = monthlyData.map((item) =>
      formatMonthYear(item.year, item.month)
    );

    return { income, salariesGA, salariesSales, categories };
  };

  const initializeCharts = () => {
    console.log("ExpenseSummary - Initializing charts with data:", reportData);

    const {
      roaData,
      roeData,
      categories: roaRoeCategories,
    } = processRoeRoaData();
    const { data: pieData, labels: pieLabels } = processExpensesPieData();
    const { series: monthlyExpensesSeries, categories: monthlyCategories } =
      processMonthlyExpensesData();
    const {
      income,
      salariesGA,
      salariesSales,
      categories: wagesCategories,
    } = processWagesRevenueData();

    console.log("ExpenseSummary - Processed data:", {
      roaDataLength: roaData.length,
      pieDataLength: pieData.length,
      monthlySeriesLength: monthlyExpensesSeries.length,
      incomeDataLength: income.length,
    });

    // Chart colors
    const colors = {
      roaRoe: ["#4a90e2", "#ff6b47"],
      expensesPie: [
        "#1f4e79",
        "#20b2aa",
        "#ff7f50",
        "#4db6ac",
        "#95a5a6",
        "#5d6d7e",
        "#bdc3c7",
        "#ffab91",
        "#9575cd",
        "#ba68c8",
        "#90a4ae",
      ],
      monthlyExpenses: [
        "#1f4e79",
        "#20b2aa",
        "#ff7f50",
        "#4db6ac",
        "#95a5a6",
        "#5d6d7e",
        "#bdc3c7",
        "#ffab91",
        "#9575cd",
        "#ba68c8",
        "#90a4ae",
        "#4a6fa5",
        "#2d5f5f",
        "#5f9ea0",
        "#8b7d82",
        "#4682b4",
        "#b0c4de",
        "#dda0dd",
        "#87ceeb",
        "#f0e68c",
        "#d3d3d3",
      ],
      wagesRevenue: ["#20b2aa", "#4a4a9a", "#ff7f50"],
    };

    // 1. ROA and ROE Line Chart
const roaRoeOptions = {
      series: [
        {
          name: "ROA",
          data: roaData,
        },
        {
          name: "ROE",
          data: roeData,
        },
      ],
      chart: {
        type: "line",
        height: 300,
        toolbar: { show: false },
        background: "transparent",
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          if (val === null || val === undefined || isNaN(val)) return "0";
          return val + "%";
        },
        style: {
          fontSize: "14px",
          colors: ["#333"],
          fontWeight: "500",
        },
        background: {
          enabled: false,
        },
        offsetY: -5,
      },
      stroke: {
        curve: "smooth",
        width: 2,
      },
      xaxis: {
        categories: roaRoeCategories,
        labels: {
          style: {
            colors: "#666",
            fontSize: "14px",
          },
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
      },
      yaxis: {
        show: false,
      },
      colors: colors.roaRoe,
      markers: {
        size: 4,
        strokeColors: "#fff",
        strokeWidth: 1,
        hover: { size: 6 },
      },
      legend: {
        position: "bottom",
        horizontalAlign: "center",
        offsetY: 0,
        markers: {
          width: 8,
          height: 8,
          radius: 4,
        },
        labels: {
          colors: ["#333"],
          useSeriesColors: false,
          fontSize: "13px",
        },
        itemMargin: {
          horizontal: 20,
          vertical: 0,
        },
      },
      tooltip: {
        y: {
          formatter: function (val) {
            if (val === null || val === undefined || isNaN(val)) return "0";
            return val + "%";
          },
        },
      },
      
      grid: {
        show: false,
        padding: {
          left: 25,
          right: 25,
          top: 20,
          bottom: 0,
        },
      },
      annotations: {
        yaxis: [
          {
            y: 0,
            borderColor: "#999",
            borderWidth: 1,
            strokeDashArray: 0,
            opacity: 1,
          },
        ],
      },
    };

    // 2. Expenses Pie Chart
    const expensesPieOptions = {
      series: pieData,
      chart: {
        type: "pie",
        height: 400,
        toolbar: { show: false },
      },
      labels: pieLabels,
      colors: colors.expensesPie,
      dataLabels: {
        enabled: true,
        formatter: function (val, opts) {
          if (!opts || !opts.w || !opts.w.globals || !opts.w.globals.series)
            return "";
          const value = opts.w.globals.series[opts.seriesIndex];
          return formatCurrency(value);
        },
        style: {
          fontSize: "11px",
          colors: ["#fff"],
          fontWeight: "500",
        },
        dropShadow: {
          enabled: false,
        },
      },
      legend: {
        position: "right",
        fontSize: "12px",
        fontWeight: "400",
        markers: {
          width: 10,
          height: 10,
          radius: 5,
        },
        labels: {
          colors: "#333",
          useSeriesColors: false,
          fontFamily: "Calibri", // 👈 Add this line

        },
        itemMargin: {
          horizontal: 5,
          vertical: 3,
        },
        offsetX: 0,
      },
      plotOptions: {
        pie: {
          dataLabels: {
            offset: 0,
          },
        },
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return formatCurrency(val, { showZero: true });
          },
        },
      },
      stroke: {
        show: false,
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            legend: { position: "bottom" },
          },
        },
      ],
    };

    // 3. Monthly Expenses Stacked Chart
    const expensesMonthlyOptions = {
      series: monthlyExpensesSeries,
      chart: {
        type: "bar",
        height: 450,
        stacked: true,
        toolbar: { show: false },
        background: "transparent",
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: "60%",
          dataLabels: {
            total: {
              enabled: true,
              offsetY: -25,
              style: {
                fontSize: "12px",
                fontWeight: "600",
                color: "#333",
              },  
              formatter: function (val) {
                return formatCurrency(val);
              },
            },
          },
        },
      },
      dataLabels: { 
        enabled: false,
        formatter: function(val) {
          return formatCurrency(val);
        },
        style: {
          fontSize: "11px",
          fontWeight: "500",
          colors: ["#333"],
        },
        offsetY: -5,
        background: {
          enabled: false,
        },
        dropShadow: {
          enabled: false,
        },
      },
      xaxis: {
        categories: monthlyCategories,
        labels: {
          style: {
            colors: "#666",
            fontSize: "14px",
          },
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
      },
      yaxis: {
        show: false,
      },
      colors: colors.monthlyExpenses,
      legend: {
        position: "bottom",
        fontSize: "11px",
        fontWeight: "400",
        markers: {
          width: 8,
          height: 8,
          radius: 4,
        },
        labels: {
          colors: "#333",
          useSeriesColors: true,
        },
        itemMargin: {
          horizontal: 8,
          vertical: 3,
        },
        offsetY: 10,
        onItemClick: {
          toggleDataSeries: true,
        },
        onItemHover: {
          highlightDataSeries: true,
        },
      },
              tooltip: {
          y: {
            formatter: function (val) {
              return formatCurrency(val, { showZero: true });
            },
          },
        },
      grid: {
        show: false,
        padding: {
          left: 25,
          right: 25,
          top: 20,
          bottom: 0,
        },
      },
      stroke: {
        show: false,
      },
    };

    // 4. Wages vs Revenue Chart (using real data from API)
    const wagesRevenueOptions = {
      series: [
        {
          name: "Income",
          type: "line",
          data: income,
        },
        {
          name: "Salaries - G&A",
          type: "column",
          data: salariesGA,
        },
        {
          name: "Salaries - Sales",
          type: "column",
          data: salariesSales,
        },
      ],
      chart: {
        height: 400,
        type: "line",
        stacked: true,
        toolbar: { show: false },
        background: "transparent",
      },
      dataLabels: {
        enabled: true,
        enabledOnSeries: [0],
        formatter: function (val, opts) {
          if (opts.seriesIndex === 0) {
            if (val === null || val === undefined || isNaN(val)) return "";
            return "$" + val + "m";
          }
          return "";
        },
        style: {
          fontSize: "14px",
          colors: ["#20b2aa"],
          fontWeight: "500",
        },
        offsetY: -10,
        background: {
          enabled: false,
        },
        dropShadow: {
          enabled: false,
        },
      },
      stroke: {
        width: [2, 0, 0],
        curve: "smooth",
      },
      plotOptions: {
        bar: {
          columnWidth: "40%",
          height: 1900,
          dataLabels: {
            total: {
              enabled: true,
              offsetY: -20,
              style: {
                fontSize: "14px",
                fontWeight: "500",
                color: "#333",
              },
              formatter: function (val) {
                if (val === null || val === undefined || isNaN(val))
                  return "$0";
                if (val >= 1) {
                  return "$" + val + "m";
                } else {
                  return "$" + (val * 1000) + "k";
                }
              },
            },
          },
        },
      },
      fill: {
        opacity: [1, 1, 1],
      },
      labels: wagesCategories,
      markers: {
        size: [5, 0, 0],
        fontSize: "14px",
        strokeColors: "#fff",
        strokeWidth: 2,
        fillOpacity: 1,
        hover: {
          size: 7,
        },
      },
      xaxis: {
        labels: {
          style: {
            colors: "#666",
            fontSize: "12px",
          },
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
      },
      yaxis: {
        show: false,
      },
      colors: colors.wagesRevenue,
      legend: {
        position: "bottom",
        horizontalAlign: "center",
        fontSize: "14px",
        fontWeight: "400",
        markers: {
          width: 8,
          height: 8,
          radius: 4,
        },
        labels: {
          colors: "#333",
          useSeriesColors: false,
        },
        itemMargin: {
          horizontal: 15,
          vertical: 4,
        },
        offsetY: 10,
        onItemClick: {
          toggleDataSeries: false,
        },
        onItemHover: {
          highlightDataSeries: false,
        },
      },
      tooltip: {
        shared: true,
        intersect: false,
        y: [
          {
            formatter: function (val) {
              if (val === null || val === undefined || isNaN(val)) return "$0";
              return "$" + val + " million";
            },
          },
          {
            formatter: function (val) {
              if (val === null || val === undefined || isNaN(val)) return "$0";
              return "$" + val + " million";
            },
          },
          {
            formatter: function (val) {
              if (val === null || val === undefined || isNaN(val)) return "$0";
              return "$" + val + " million";
            },
          },
        ],
      },
      grid: {
        show: false,
        padding: {
          left: 25,
          right: 25,
          top: 20,
          bottom: 0,
        },
      },
    };

    // Clear existing charts before rendering new ones
    const clearAndRenderChart = (ref, options, chartName) => {
      if (ref.current) {
        // Clear any existing chart
        ref.current.innerHTML = "";

        // Wait a tick before rendering to ensure DOM is cleared
        setTimeout(() => {
          if (ref.current) {
            try {
              console.log(`ExpenseSummary - Rendering ${chartName} chart`);
              const chart = new ApexCharts(ref.current, options);
              chart.render();
            } catch (error) {
              console.error(
                `ExpenseSummary - Error rendering ${chartName} chart:`,
                error
              );
            }
          }
        }, 10);
      }
    };

    // Only render charts if we have data for them
    if (roaData.length > 0 || roeData.length > 0) {
      clearAndRenderChart(roaRoeRef, roaRoeOptions, "ROA/ROE");
    }

    if (pieData.length > 0) {
      clearAndRenderChart(expensesPieRef, expensesPieOptions, "Expenses Pie");
    }

    if (monthlyExpensesSeries.length > 0) {
      clearAndRenderChart(
        expensesMonthlyRef,
        expensesMonthlyOptions,
        "Monthly Expenses"
      );
    }

    if (income.length > 0) {
      clearAndRenderChart(
        wagesRevenueRef,
        wagesRevenueOptions,
        "Wages vs Revenue"
      );
    }
  };

  // Enhanced loading component
  const LoadingComponent = () => (
    <div className="min-h-screen p-5">
      <div className="max-w-6xl mx-auto bg-white flex flex-col shadow p-10 mb-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            {/* Loading spinner */}
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mb-4"></div>
            <div className="text-xl text-gray-600 mb-2">
              Loading expense data...
            </div>
            <div className="text-sm text-gray-500">
              Please wait while we fetch your expense information
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Show loading if data is not properly loaded
  if (!isDataLoaded()) {
    return <LoadingComponent />;
  }

  // Add a fallback if reportData exists but has no usable data
  const hasAnyUsableData = () => {
    const { roaData, roeData } = processRoeRoaData();
    const { data: pieData } = processExpensesPieData();
    const { series: monthlyExpensesSeries } = processMonthlyExpensesData();
    const { income } = processWagesRevenueData();

    return (
      roaData.length > 0 ||
      roeData.length > 0 ||
      pieData.length > 0 ||
      monthlyExpensesSeries.length > 0 ||
      income.length > 0
    );
  };

  if (!hasAnyUsableData()) {
    return (
      <div className="min-h-screen p-5">
        <div className="max-w-6xl mx-auto bg-white flex flex-col shadow p-10 mb-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-xl text-gray-600 mb-2">
                No expense data available
              </div>
              <div className="text-sm text-gray-500">
                The data structure is available but charts cannot be rendered
              </div>
              <div className="text-xs text-gray-400 mt-2">
                Available data: {Object.keys(reportData || {}).join(", ")}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-5">
      {/* Main Container */}
      <div className="max-w-6xl mx-auto bg-white flex flex-col shadow p-10 mb-8">
        {/* Header Section */}
        <div className="flex items-center justify-between gap-4 border-b-4 border-blue-900 pb-2">
          <h1
            className="text-4xl font-bold text-gray-800 m-0"
            style={headerTextStyle}
          >
            Expense Summary
          </h1>
          <p className="text-lg text-gray-600 m-0" style={subHeadingTextStyle}>
            January 2025 | Acme Print
          </p>
        </div>

        {/* Return on Assets and Equity Chart */}
        <div className="bg-white p-6 border-b-4 border-blue-900">
          <div
            className="text-2xl font-semibold text-teal-600 mb-5"
            style={subHeadingTextStyle}
          >
            Return on Assets and Equity
          </div>
          <div ref={roaRoeRef}></div>

          {/* Metrics Explanations */}
          <div className="mt-6">
            <div className="mb-2">
              <div
                className="text-teal-600 text-xl font-semibold"
                style={{ ...subHeadingTextStyle, fontWeight: "lighter" }}
              >
                Return on Assets
              </div>
              <p
                className="text-gray-700 leading-relaxed"
                style={contentTextStyle}
              >
                Indicates how well Acme Print is using capital invested in
                Assets to generate Total Income. The higher the return, the more
                productive and efficient management is in utilizing economic
                resources the business has.
              </p>
            </div>

            <div className="">
              <div
                className="text-teal-600 text-xl font-semibold"
                style={{ ...subHeadingTextStyle, fontWeight: "lighter" }}
              >
                Return on Equity
              </div>
              <p
                className="text-gray-700 leading-relaxed"
                style={contentTextStyle}
              >
                Indicates how efficient company management is at generating
                growth from its Equity financing. Because Equity is equal to a
                company's Assets minus Liabilities, ROE is also considered the
                Return on Net Assets.
              </p>
            </div>
          </div>
        </div>

        {/* Expenses: Top Accounts Pie Chart */}
        <div className="bg-white p-6 border-b-4 border-blue-900">
          <div
            className="text-2xl font-semibold text-teal-600 mb-5"
            style={subHeadingTextStyle}
          >
            Expenses: Top Accounts
          </div>
          <div ref={expensesPieRef} className="mb-12"></div>
        </div>

        {/* Expenses: Top Accounts Monthly */}
        <div className="bg-white p-6 border-b-4 border-blue-900">
          <div
            className="text-2xl font-semibold text-teal-600 mb-5"
            style={subHeadingTextStyle}
          >
            Expenses: Top Accounts Monthly
          </div>
          <div ref={expensesMonthlyRef}></div>
        </div>

        {/* Expenses: Wages Vs Revenue Monthly */}
        <div className="bg-white p-6 border-b-4 border-blue-900">
          <div
            className="text-2xl font-semibold text-teal-600 mb-5"
            style={subHeadingTextStyle}
          >
            Expenses: Wages Vs Revenue Monthly
          </div>
          <div ref={wagesRevenueRef} className="mb-32"></div>
        </div>
      </div>
    </div>
  );
};

export default ExpenseSummaryDashboard;